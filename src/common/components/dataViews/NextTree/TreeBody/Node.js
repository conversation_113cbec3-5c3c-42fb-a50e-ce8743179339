/* global $ */
import classnames from 'classnames';
import PropTypes from 'prop-types';
import React from 'react';
import uuid from 'uuid';

import Icon from '../../../utils/Icon';
import Spinner from '../../../utils/Spinner';
import {
  nodeIdPropType,
  selectionVisualsPropType,
  treeAdapterPropTypes,
} from '../propTypes';
import {
  getNodeTitle,
  getNodeTooltip,
  renderNodeAddons,
} from '../store/actionHandlers/common/adapterActions';
import { setNodeExpanded, toggleNodeSelected } from '../store/actions';
import { treeContext, treeSelectionVisuals } from '../store/selectors';
import withNextTreeStoreConnect from '../store/withNextTreeStoreConnect';
import styles from './Node.scss';
import Nodes from './Nodes';

const mapStateToProps = (state, { id, tree: treeId, plugins }) => {
  const tree = state[treeId];

  // TODO remove after router fix
  if (!tree) {
    return {};
  }
  const {
    selection,
    highlighting,
    expansion,
    filter,
    nodeClassNames,
    nodeIcon,
    nodeCustomText,
    totalLeafCounter,
    nodesMeta: nodesMetaPlugin,
    lazyLoading,
    dynamicLeaves,
    nodePopover,
  } = plugins;

  const model = tree.models[id];
  const leaf =
    tree.children[id] === undefined ||
    !!(dynamicLeaves && dynamicLeaves.shouldBeLeaf(tree, id, { plugins }));
  const visible = filter ? filter.nodeIsVisible(tree, id) : true;
  const selected = selection ? selection.nodeIsSelected(tree, id) : false;
  const selectable = selection
    ? selection.nodeIsSelectable(model, tree.selection)
    : false;
  const highlighted = highlighting
    ? highlighting.nodeIsHighlighted(tree, id)
    : false;
  const expanded = expansion ? expansion.nodeIsExpanded(tree, id) : true;
  const className = nodeClassNames
    ? nodeClassNames.getNodeClassName(tree, id)
    : null;
  const totalLeafCount = totalLeafCounter
    ? totalLeafCounter.getTotalLeafCount(tree, id)
    : 0;
  const autoShowTotalLeafCount = totalLeafCounter
    ? totalLeafCounter.autoShow
    : false;
  const nodesMeta = nodesMetaPlugin
    ? nodesMetaPlugin.getNodesMeta(tree, id, { plugins })
    : null;
  const childrenLoading = lazyLoading ? lazyLoading.isLoading(tree, id) : false;
  const selectionIsIntermediate =
    selection && selection.nodeIsIntermediate
      ? selection.nodeIsIntermediate(tree, id)
      : false;
  const isAllChildrenSelected =
    selection && selection.nodeIsAdvanced
      ? selection.nodeIsAdvanced(tree, id)
      : false;
  const isExpandOnSelection = expansion && expansion.isExpandOnSelection;
  const nodeIconName = nodeIcon ? nodeIcon.getNodeIconName(tree, id) : '';
  const customText = nodeCustomText
    ? nodeCustomText.getNodeCustomText(tree, id)
    : null;
  const nodePopoverText = nodePopover
    ? nodePopover.getNodePopoverText(tree, id)
    : null;
  const context = treeContext(treeId)(state);

  return {
    state: tree,
    model,
    leaf,
    visible,
    selected,
    selectionIsIntermediate,
    isAllChildrenSelected,
    selectable,
    highlighted,
    expanded,
    className,
    totalLeafCount,
    childrenLoading,
    nodesMeta,
    context,
    autoShowTotalLeafCount,
    isExpandOnSelection,
    nodeIconName,
    customText,
    nodePopoverText,
    selectionVisuals: treeSelectionVisuals(tree, plugins)(state),
  };
};

const mapDispatchToPropsObject = (
  dispatch,
  { tree, id, adapter, plugins },
) => ({
  toggleExpanded: expanded =>
    dispatch(setNodeExpanded(tree, id, expanded, { adapter, plugins })),

  toggleSelected: () => {
    dispatch(toggleNodeSelected(tree, id, { adapter, plugins }));
  },
});

@withNextTreeStoreConnect(mapStateToProps, mapDispatchToPropsObject)
export default class Node extends React.PureComponent {
  static propTypes = {
    tree: PropTypes.string.isRequired,
    id: nodeIdPropType.isRequired,
    model: PropTypes.any.isRequired,

    visible: PropTypes.bool.isRequired,
    leaf: PropTypes.bool.isRequired,
    selected: PropTypes.bool.isRequired,
    selectionIsIntermediate: PropTypes.bool.isRequired,
    isAllChildrenSelected: PropTypes.bool.isRequired,
    selectable: PropTypes.bool.isRequired,
    highlighted: PropTypes.bool.isRequired,
    expanded: PropTypes.bool.isRequired,
    className: PropTypes.string,
    toggleExpanded: PropTypes.func.isRequired,
    toggleSelected: PropTypes.func.isRequired,
    totalLeafCount: PropTypes.number.isRequired,
    autoShowTotalLeafCount: PropTypes.bool.isRequired,
    childrenLoading: PropTypes.bool.isRequired,
    selectionVisuals: selectionVisualsPropType,
    nodesMeta: PropTypes.object,
    isExpandOnSelection: PropTypes.bool,
    nodeIconName: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.array,
      PropTypes.object,
    ]),
    customText: PropTypes.string,
    nodePopoverText: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),

    plugins: PropTypes.object.isRequired,
    adapter: treeAdapterPropTypes.isRequired,

    context: PropTypes.any,
  };

  static defaultProps = {
    className: null,
    nodesMeta: null,
    selectionVisuals: null,
    context: null,
    isExpandOnSelection: false,
    nodeIconName: null,
    customText: null,
    nodePopoverText: null,
  };

  handleExpandClick = () => {
    const { toggleExpanded, expanded } = this.props;
    toggleExpanded(!expanded);
  };

  handleClick = e => {
    const {
      selectable,
      toggleSelected,
      isExpandOnSelection,
      selected,
      expanded,
    } = this.props;

    if (isExpandOnSelection && !selected && !expanded) {
      this.handleExpandClick(e);
      e.preventDefault();
      return toggleSelected();
    }

    if (!selectable) {
      return this.handleExpandClick(e);
    }

    e.preventDefault();
    toggleSelected();
  };

  setLabelRef = label => {
    const { selectable, selectionVisuals } = this.props;

    if (this.$label) {
      this.$label.off('mouseenter mouseleave');
      this.$label = null;
    }

    if (label && selectable && selectionVisuals === 'box') {
      this.$label = $(label);
      // this.$label.hover(this.handleHoverOn, this.handleHoverOff);
    }
  };

  handleHoverOn(e) {
    const $label = $(e.currentTarget);
    $label.toggleClass('jstree-hovered', true);
  }

  handleHoverOff(e) {
    const $label = $(e.currentTarget);
    $label.toggleClass('jstree-hovered', false);
  }

  render() {
    const {
      leaf,
      selected,
      selectable,
      selectionIsIntermediate,
      isAllChildrenSelected,
      highlighted,
      model,
      tree,
      id,
      expanded,
      visible,
      className: nodeClassName,
      childrenLoading,
      totalLeafCount,
      autoShowTotalLeafCount,
      nodesMeta,
      selectionVisuals,
      plugins,
      adapter,
      context,
      nodeIconName,
      customText: nodeCustomText,
      nodePopoverText,
      state,
      handleClear,
    } = this.props;

    if (!visible) {
      return null;
    }

    if (adapter && adapter.canRender) {
      const isRenderable = adapter.canRender({
        id,
        model,
        leaf,
        nodesMeta,
        context,
        state,
      });
      if (!isRenderable) {
        return null;
      }
    }

    const className = classnames(styles.node, nodeClassName, 'jstree-node', {
      'jstree-leaf': leaf || model?.hasChildren === false,
      'jstree-open': !leaf && expanded,
      'jstree-closed': !leaf && !expanded,
    });

    const labelClassName = classnames(
      styles.anchor,
      styles.hoverEffect,
      'jstree-anchor',
      {
        'jstree-clicked': selected || highlighted || isAllChildrenSelected,
        [styles.noBox]: selectionVisuals !== 'box',
      },
    );

    let controlButton = null;
    if (
      (selectionVisuals === 'checkbox' || selectionVisuals === 'radio') &&
      (selectable || selected)
    ) {
      controlButton = (
        <i
          className={classnames('jstree-icon jstree-checkbox', {
            'jstree-undetermined':
              selectionIsIntermediate && !isAllChildrenSelected,
            [styles.disabled]: !selectable,
          })}
        />
      );
    }

    let nodeIcon = null;
    if (nodeIconName) {
      if (typeof nodeIconName === 'string') {
        nodeIcon = [{ name: nodeIconName }];
      } else {
        nodeIcon = Array.isArray(nodeIconName) ? nodeIconName : [nodeIconName];
      }
    }

    let nodePopover = null;
    if (nodePopoverText) {
      if (typeof nodePopoverText === 'string') {
        nodePopover = <span className={styles.popover}>{nodePopoverText}</span>;
      } else {
        nodePopover = (
          <span className={styles.popover}>
            <ul>
              {nodePopoverText.map(item => (
                <li key={item}>{item}</li>
              ))}
            </ul>
          </span>
        );
      }
    }

    let customText = null;
    if (nodeCustomText) {
      customText = <b>{nodeCustomText} </b>;
    }

    const nodeTitle = getNodeTitle(adapter, model, {
      nodesMeta,
      context,
      totalLeafCount,
    });

    const nodeTooltip = getNodeTooltip(adapter, model, {
      nodesMeta,
      context,
      totalLeafCount,
    });

    return (
      <li aria-selected={selected || highlighted} className={className}>
        <i
          className="jstree-icon jstree-ocl"
          onClick={this.handleExpandClick}
        />

        <label
          ref={this.setLabelRef}
          className={labelClassName}
          onClick={this.handleClick}
        >
          {controlButton}
          {nodeIcon &&
            nodeIcon.map(item => (
              <Icon
                key={uuid.v4()}
                badge={item.badge ? item.badge : ''}
                className={classnames(item.className, 'mr-10 node-icon')}
                name={item.name ? item.name : ''}
                type={item.type ? item.type : ''}
              />
            ))}
          {customText}

          <label className="show-edit-level edit-link" title={nodeTooltip}>
            {nodeTitle}
            {autoShowTotalLeafCount &&
              totalLeafCount > 0 &&
              ` (${totalLeafCount})`}
          </label>

          {childrenLoading && <Spinner className={styles.persistent} />}

          {renderNodeAddons(adapter, model, {
            nodesMeta,
            context,
            handleClear,
          })}

          {nodePopover}
        </label>

        {!leaf && !childrenLoading && (
          <Nodes
            adapter={adapter}
            handleClear={handleClear}
            parentId={id}
            plugins={plugins}
            tree={tree}
          />
        )}
      </li>
    );
  }
}

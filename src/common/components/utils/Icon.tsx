import classnames from 'classnames';
import React, { useMemo } from 'react';

import { TIcon } from '../../propTypes';

import styleIcon from './Icon.scss';

export type TIconType =
  | 'fa'
  | ''
  | 'material'
  | 'tree-icon'
  | 'pgt'
  | 'ed'
  | 'image';

export interface IIcon {
  name?: TIcon;
  src?: string;
  hoverName?: TIcon;
  className?: string;
  style?: object;
  badge?: string | number;
  type?: TIconType;
  hoverTitle?: string;
  hasBackground?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
}

const Icon: React.FC<IIcon> = ({
  name,
  src,
  hoverName,
  className = '',
  style = {},
  badge = '',
  type = '',
  hoverTitle = '',
  hasBackground = false,
  isDisabled = false,
  onClick,
}) => {
  const prefix = useMemo<string>(() => {
    switch (type) {
      case 'fa':
        return 'fa fa-';
      case 'pgt':
        return 'pgt';
      case 'ed':
        return 'ed';
      case 'image':
        return '';
      default:
        return 'icon-';
    }
  }, [type]);

  const iconName = useMemo<string>(() => {
    switch (type) {
      case 'fa':
        return name?.startsWith(prefix) ? name : `fa fa-${name}`;
      case 'material':
        return 'material-icons';
      case 'tree-icon':
        return styleIcon.treeIcon;
      case 'image':
        return styleIcon.imageIcon;
      default:
        return name && name.startsWith(prefix) ? name : `icon-${name}`;
    }
  }, [prefix, name, type]);

  const iconHoverName = useMemo<string | undefined>(() => {
    if (!hoverName) {
      return undefined;
    }

    switch (type) {
      case 'fa':
        return hoverName.startsWith(prefix) ? hoverName : `fa fa-${hoverName}`;
      case 'material':
        return 'material-icons';
      case 'tree-icon':
        return styleIcon.treeIcon;
      case 'image':
        return styleIcon.imageIcon;
      default:
        return hoverName && hoverName.startsWith(prefix)
          ? hoverName
          : `icon-${hoverName}`;
    }
  }, [prefix, hoverName, type]);

  const backgroundClass = hasBackground ? styleIcon.iconWithBackground : '';

  return (
    <>
      {type === 'image' && <img className={iconName} src={src} />}
      {
        /* eslint-disable react/forbid-dom-props */
        type !== 'image' && (
          <i
            className={classnames(
              className,
              iconName,
              backgroundClass,
              styleIcon.hoverContainer,
              { [styleIcon.disabled]: isDisabled },
            )}
            style={style}
            title={hoverTitle}
            onClick={onClick}
          >
            {badge && <span className="notification-counts">{badge}</span>}

            {iconHoverName ? (
              <i
                className={classnames(
                  className,
                  iconHoverName,
                  backgroundClass,
                  styleIcon.hoverContent,
                )}
                style={style}
                title={hoverTitle}
              />
            ) : null}
          </i>
        )
      }
    </>
  );
};

export default Icon;

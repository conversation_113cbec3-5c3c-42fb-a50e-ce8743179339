.wrapper {
  display: flex;
  align-items: end;
}
.alignItemsNormal {
  align-items: unset;
}
@media screen and (max-width: 1024px) {
  .wrapper {
    display: unset;
  }
}
.dragRow {
  width: inherit;
  display: inline-block;
  margin-left: 20px;
}
.dragRowView {
  width: inherit;
  display: inline-block;
  .dragBox,
  .optionBox {
    display: none !important;
  }
  :global(.radio label),
  :global(.checkbox label) {
    padding-left: 18px !important;
  }
}
.dragBox {
  float: left;
  width: 30px;
  height: 30px;
}
.alphaOrder {
  float: left;
  font-weight: bold;
  width: 20px;
  color: grey;
  font-size: 17px;
  min-height: 10px;
}
.orderBox {
  float: left;
}
.tickBox {
  float: left;
  width: 48px;
  margin-top: 3px;
  :global(.radio),
  :global(.checkbox) {
    margin: 0px !important;
  }
}
.attachmentBox {
  margin-left: 8%;
  margin-right: 7%;
}

.selectBox {
  width: 85px;
  width: 85px !important;
  float: left;
  min-height: 1px;
  :global(.dropdown-toggle) {
    padding: 2px 0px 17px 0 !important;
  }
  :global(.captionLabel) {
    margin-top: 3px !important;
  }
}
.marginLabel {
  margin-left: 14px;
}

.inputBox {
  float: left;
  width: 55%;
  margin-top: 1px;
  margin-bottom: 12px;
  :global(textarea.form-control) {
    padding: 8px 0px !important;
    margin: 0px !important;
  }
  :global(input.form-control),
  :global(.form-group) {
    padding: 0px !important;
    margin: 0px !important;
  }
  :global(.col-lg-12) {
    padding-left: 0px !important;
  }
}
.inputBoxText {
  margin-top: -7px;
  margin-bottom: 10px;
}
.inputBoxView {
  float: left;
  width: 100%;
  margin-top: 3px;
  :global(.form-control) {
    margin-top: -10px !important;
    border: 0px !important;
  }
  :global(.col-lg-12) {
    padding-left: 0px !important;
  }
  :global(.form-group-material) {
    margin-top: -10px !important;
    // margin-bottom: 0px !important;
  }
}
.optionBox {
  float: left;
  :global(.strike) {
    padding: 0px !important;
  }
  :global(.panel-buttons-wrapper) {
    margin-bottom: 0px !important;
  }
}
.optionBoxLabel {
  float: left;
}
.drodownBox {
  .tickBox {
    width: 60px !important;
  }
}

.removeIcon {
  color: red;
  width: 21px;
  height: 21px;
  float: left;
  margin-top: 7px;
}

.addWrap {
  margin-left: 10px;
  float: left;
}

.removeBtn {
  right: 0;
}
@media screen and (max-width: 500px) {
  .dragBox {
    margin-right: 13px;
  }
}
@media screen and (max-width: 520px) {
}

@media only screen and (max-width: 300px) {
  .inputBox {
    width: 75%;
  }
  .drodownBox {
    .inputBox {
      width: 100% !important;
    }
    .attachmentBox {
      margin-left: 0% !important;
    }
  }
}
.fullWidth {
  width: 100% !important;
  :global(.strike) {
    margin-bottom: 0px !important;
  }
}
.dFlex {
  display: flex;
}
.inputPointHeight {
  height: 43px;
}

.pointsSelect{
  label{
    display: none !important;
  }
}

.pointsField { 
  padding-bottom: 8px !important;
}
@media screen and (max-width: 500px) {
  .pointsField {
    margin-left: 92px !important;
  }
}
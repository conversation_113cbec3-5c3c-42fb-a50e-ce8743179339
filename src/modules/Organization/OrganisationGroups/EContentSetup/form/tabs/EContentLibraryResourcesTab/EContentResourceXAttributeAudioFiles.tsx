import React from 'react';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import SelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import EContentResourceXAttributeSubSection from './EContentResourceXAttributeSubSection';
import {
  oneToTenTwentyToHundredTwoHundredToFiveHundred,
  oneToTenTwentyToHundredTwoThousandToFourThousand,
  zeroToTenTwentyToHundredTwoThousandToFourThousand,
} from '../../../model/EContentAttributesOptions';
import { RETURN_TYPE } from '../../../../../../../common/components/controls/base/SelectBox';
import useHandleMinMaxFields from './hooks/useHandleMinMaxFields';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import styles from '../../../../../../../common/components/containers/EntityForm/fields/CharactersField.scss';
import EContentResourceXAttributeAudioValidation from './EContentResourceXAttributeAudioValidation';

const EContentResourceXAttributeAudioFiles: React.FC<{
  prefix?: string;
}> = ({ prefix = 'cookedAttributes' }) => {
  const t = useT();

  const { isChecked } = useDependsOnFields({
    isChecked: `${prefix}.audioFile.isChecked`,
  });

  const optionsForMaxField = useHandleMinMaxFields({
    minFieldName: `${prefix}.minNumberOfAudioFiles.value`,
    maxFieldName: `${prefix}.maxNumberOfAudioFiles.value`,
    options: oneToTenTwentyToHundredTwoThousandToFourThousand,
  });

  return (
    <>
      <EntityFormFieldSet>
        <CheckboxField
          columns={1}
          label={t('Audio Files')}
          name={`${prefix}.audioFile.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <div className="pl-20">
          <AnimatedTitle
            isRequired
            className={styles.labelTitle}
            placeholder={t('Number of Audio Files')}
          />
          <EntityFormFieldSet>
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('From')}
              name={`${prefix}.minNumberOfAudioFiles.value`}
              options={zeroToTenTwentyToHundredTwoThousandToFourThousand}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('To')}
              name={`${prefix}.maxNumberOfAudioFiles.value`}
              options={optionsForMaxField}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              defaultValue={5}
              infoMessage="MB"
              label={t('Maximum Size of Each Audio File')}
              name={`${prefix}.maxSizeOfEachAudioFile.value`}
              options={oneToTenTwentyToHundredTwoHundredToFiveHundred}
              returnType={RETURN_TYPE.NUMBER}
            />
          </EntityFormFieldSet>
          <EContentResourceXAttributeSubSection
            label={t('Audio Caption')}
            name="audioFileCaption"
          />
          <EContentResourceXAttributeAudioValidation
            label={t('Audio Validation')}
            name="audioValidation"
          />
        </div>
      ) : null}
    </>
  );
};

export default EContentResourceXAttributeAudioFiles;

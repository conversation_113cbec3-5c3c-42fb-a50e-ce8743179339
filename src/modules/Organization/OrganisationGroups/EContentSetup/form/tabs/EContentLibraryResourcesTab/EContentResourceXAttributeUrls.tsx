import React from 'react';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import SelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import EContentResourceXAttributeSubSection from './EContentResourceXAttributeSubSection';
import {
  zeroToTenTwentyToHundredTwoThousandToFourThousand,
  oneToTenTwentyToHundredTwoThousandToFourThousand,
} from '../../../model/EContentAttributesOptions';
import { RETURN_TYPE } from '../../../../../../../common/components/controls/base/SelectBox';
import useHandleMinMaxFields from './hooks/useHandleMinMaxFields';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import styles from '../../../../../../../common/components/containers/EntityForm/fields/CharactersField.scss';

const EContentResourceXAttributeUrls: React.FC<{
  prefix?: string;
}> = ({ prefix = 'cookedAttributes' }) => {
  const t = useT();

  const { isChecked } = useDependsOnFields({
    isChecked: `${prefix}.url.isChecked`,
  });

  const optionsForMaxField = useHandleMinMaxFields({
    minFieldName: `${prefix}.minNumberOfUrls.value`,
    maxFieldName: `${prefix}.maxNumberOfUrls.value`,
    options: oneToTenTwentyToHundredTwoThousandToFourThousand,
  });

  return (
    <>
      <EntityFormFieldSet>
        <CheckboxField
          columns={1}
          label={t('URLs')}
          name={`${prefix}.url.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <div className="pl-20">
          <AnimatedTitle
            isRequired
            className={styles.labelTitle}
            placeholder={t('Number of URLs')}
          />
          <EntityFormFieldSet>
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('From')}
              name={`${prefix}.minNumberOfUrls.value`}
              options={zeroToTenTwentyToHundredTwoThousandToFourThousand}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('To')}
              name={`${prefix}.maxNumberOfUrls.value`}
              options={optionsForMaxField}
              returnType={RETURN_TYPE.NUMBER}
            />
          </EntityFormFieldSet>
          <EContentResourceXAttributeSubSection
            label={t('URL Caption')}
            name="urlCaption"
          />
        </div>
      ) : null}
    </>
  );
};

export default EContentResourceXAttributeUrls;

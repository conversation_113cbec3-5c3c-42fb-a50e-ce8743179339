import React from 'react';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import SelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import EContentResourceXAttributeSubSection from './EContentResourceXAttributeSubSection';
import {
  oneToTenTwentyToHundredTwoThousandToFourThousand,
  tenToTwentyToHundredToThousandToFourThousand,
  zeroToTenTwentyToHundredTwoThousandToFourThousand,
} from '../../../model/EContentAttributesOptions';
import { RETURN_TYPE } from '../../../../../../../common/components/controls/base/SelectBox';
import useHandleMinMaxFields from './hooks/useHandleMinMaxFields';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import styles from '../../../../../../../common/components/containers/EntityForm/fields/CharactersField.scss';

const EContentResourceXAttributeVideoFiles: React.FC<{
  prefix?: string;
}> = ({ prefix = 'cookedAttributes' }) => {
  const t = useT();

  const { isChecked } = useDependsOnFields({
    isChecked: `${prefix}.videoFile.isChecked`,
  });

  const optionsForMaxField = useHandleMinMaxFields({
    minFieldName: `${prefix}.minNumberOfVideoFiles.value`,
    maxFieldName: `${prefix}.maxNumberOfVideoFiles.value`,
    options: oneToTenTwentyToHundredTwoThousandToFourThousand,
  });

  return (
    <>
      <EntityFormFieldSet>
        <CheckboxField
          columns={1}
          label={t('Video Files')}
          name={`${prefix}.videoFile.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <div className="pl-20">
          <AnimatedTitle
            isRequired
            className={styles.labelTitle}
            placeholder={t('Number of Video Files')}
          />
          <EntityFormFieldSet>
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('From')}
              name={`${prefix}.minNumberOfVideoFiles.value`}
              options={zeroToTenTwentyToHundredTwoThousandToFourThousand}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('To')}
              name={`${prefix}.maxNumberOfVideoFiles.value`}
              options={optionsForMaxField}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              defaultValue={50}
              infoMessage="MB"
              label={t('Maximum Size of Each Video File')}
              name={`${prefix}.maxSizeOfEachVideoFile.value`}
              options={tenToTwentyToHundredToThousandToFourThousand}
              returnType={RETURN_TYPE.NUMBER}
            />
          </EntityFormFieldSet>
          <EContentResourceXAttributeSubSection
            label={t('Video Caption')}
            name="videoFileCaption"
          />
        </div>
      ) : null}
    </>
  );
};

export default EContentResourceXAttributeVideoFiles;
